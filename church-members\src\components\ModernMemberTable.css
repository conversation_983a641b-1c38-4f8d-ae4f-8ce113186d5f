/* Modern Member Table Styles */

.modern-member-table {
  padding: var(--spacing-xl);
}

.table-controls {
  display: flex;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-xl);
  align-items: center;
}

.search-wrapper {
  flex: 1;
  position: relative;
  max-width: 400px;
}

.search-icon {
  position: absolute;
  left: var(--spacing-md);
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-muted);
  pointer-events: none;
}

.modern-search-input {
  width: 100%;
  padding: var(--spacing-md) var(--spacing-md) var(--spacing-md) 3rem;
  border: 2px solid var(--border-color);
  border-radius: var(--radius-xl);
  font-size: 1rem;
  background: var(--surface-color);
  transition: all var(--transition-fast);
  box-shadow: var(--shadow-sm);
}

.modern-search-input:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 4px var(--primary-light), var(--shadow-md);
  background: white;
}

.filter-btn {
  width: 48px;
  height: 48px;
  border-radius: var(--radius-lg);
  background: var(--surface-color);
  border: 2px solid var(--border-color);
  color: var(--text-secondary);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all var(--transition-fast);
  cursor: pointer;
}

.filter-btn:hover,
.filter-btn.active {
  background: var(--primary-color);
  border-color: var(--primary-color);
  color: white;
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.member-cards-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(380px, 1fr));
  gap: var(--spacing-lg);
}

.modern-member-card {
  background: var(--surface-color);
  border-radius: var(--radius-xl);
  padding: var(--spacing-xl);
  box-shadow: var(--shadow-md);
  border: 2px solid var(--border-light);
  transition: all var(--transition-fast);
  position: relative;
  overflow: hidden;
}

.modern-member-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-xl);
  border-color: var(--primary-color);
}

.modern-member-card.critical {
  border-left: 6px solid var(--danger-color);
  background: linear-gradient(135deg, #fef2f2 0%, #ffffff 100%);
}

.modern-member-card.critical:hover {
  border-color: var(--danger-color);
  box-shadow: 0 20px 40px rgba(239, 68, 68, 0.15);
}

.member-card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--spacing-lg);
  gap: var(--spacing-md);
}

.member-info {
  flex: 1;
}

.member-name {
  margin: 0 0 var(--spacing-xs) 0;
  font-size: 1.25rem;
  font-weight: 700;
  color: var(--text-primary);
  line-height: 1.3;
}

.critical-badge {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-xs);
  background: var(--gradient-danger);
  color: white;
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-md);
  font-size: 0.75rem;
  font-weight: 600;
  animation: pulse 2s infinite;
  box-shadow: var(--shadow-sm);
}

.member-actions {
  display: flex;
  gap: var(--spacing-xs);
}

.action-btn {
  width: 40px;
  height: 40px;
  border-radius: var(--radius-lg);
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all var(--transition-fast);
  box-shadow: var(--shadow-sm);
}

.action-btn svg {
  width: 16px;
  height: 16px;
  display: block;
}

.action-btn.edit {
  background: var(--gradient-primary);
  color: white;
}

.action-btn.edit:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.action-btn.delete {
  background: var(--gradient-danger);
  color: white;
}

.action-btn.delete:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.member-details {
  margin-bottom: var(--spacing-lg);
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.detail-row {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  font-size: 0.875rem;
  color: var(--text-secondary);
  padding: var(--spacing-xs) 0;
}

.detail-icon {
  color: var(--primary-color);
  flex-shrink: 0;
}

.detail-label {
  font-weight: 600;
  color: var(--text-primary);
  min-width: 80px;
}

.status-badge {
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-sm);
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.status-badge.yes {
  background: var(--success-light);
  color: var(--success-color);
}

.status-badge.no {
  background: var(--border-light);
  color: var(--text-muted);
}

.attendance-section {
  border-top: 1px solid var(--border-light);
  padding-top: var(--spacing-lg);
}

.attendance-title {
  margin: 0 0 var(--spacing-md) 0;
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-primary);
}

.attendance-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-md);
}

.attendance-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-xs);
}

.attendance-date {
  font-size: 0.75rem;
  color: var(--text-secondary);
  font-weight: 600;
  text-align: center;
}

.modern-attendance-btn {
  width: 40px;
  height: 40px;
  border-radius: var(--radius-lg);
  border: 2px solid var(--border-color);
  background: var(--surface-color);
  cursor: pointer;
  transition: all var(--transition-fast);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

.modern-attendance-btn:hover {
  transform: scale(1.1);
  box-shadow: var(--shadow-lg);
}

.modern-attendance-btn.present {
  background: var(--gradient-success);
  border-color: var(--success-color);
  color: white;
}

.modern-attendance-btn.absent {
  background: var(--gradient-danger);
  border-color: var(--danger-color);
  color: white;
}

.modern-attendance-btn.unmarked {
  background: var(--border-light);
  border-color: var(--border-color);
}

.unmarked-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: var(--text-muted);
  opacity: 0.5;
}

.absence-warning {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  color: var(--danger-color);
  font-size: 0.875rem;
  font-weight: 600;
  background: var(--danger-light);
  padding: var(--spacing-sm);
  border-radius: var(--radius-md);
  border: 1px solid rgba(239, 68, 68, 0.2);
}

.modern-empty-state,
.no-results {
  text-align: center;
  padding: var(--spacing-3xl);
  color: var(--text-secondary);
}

.empty-icon {
  color: var(--text-muted);
  margin-bottom: var(--spacing-lg);
}

.modern-empty-state h3,
.no-results h3 {
  margin: var(--spacing-lg) 0 var(--spacing-sm) 0;
  color: var(--text-primary);
  font-size: 1.5rem;
}

.modern-empty-state p,
.no-results p {
  margin: var(--spacing-sm) 0;
  font-size: 1rem;
  line-height: 1.6;
}

/* Mobile responsive */
@media (max-width: 768px) {
  .member-cards-grid {
    grid-template-columns: 1fr;
  }
  
  .modern-member-card {
    padding: var(--spacing-lg);
  }
  
  .table-controls {
    flex-direction: column;
    align-items: stretch;
  }
  
  .search-wrapper {
    max-width: none;
  }
}

@media (max-width: 480px) {
  .modern-member-table {
    padding: var(--spacing-lg);
  }
  
  .modern-member-card {
    padding: var(--spacing-md);
  }
  
  .member-card-header {
    flex-direction: column;
    gap: var(--spacing-sm);
  }
  
  .member-actions {
    align-self: flex-end;
  }
  
  .attendance-grid {
    grid-template-columns: repeat(auto-fit, minmax(60px, 1fr));
  }
  
  .modern-attendance-btn {
    width: 36px;
    height: 36px;
  }
}
