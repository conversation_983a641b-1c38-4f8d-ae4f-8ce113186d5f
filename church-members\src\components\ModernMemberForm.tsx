import React, { useState, useEffect } from 'react';
import type { Member, Congregation } from '../types';
import { X, User, Phone, Heart, Users, MapPin, Calendar, BookOpen, GraduationCap, Briefcase, Star } from 'lucide-react';
import './ModernMemberForm.css';

interface ModernMemberFormProps {
  member?: Member | null;
  congregations: Congregation[];
  onSave: (member: Omit<Member, 'id' | 'createdDate' | 'lastUpdated'>) => void;
  onCancel: () => void;
}

const ModernMemberForm: React.FC<ModernMemberFormProps> = ({
  member,
  congregations,
  onSave,
  onCancel,
}) => {
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    phoneNumber: '',
    buildingAddress: '',
    bornAgainStatus: false,
    congregationGroup: '',
    // New fields for New Believers
    dateOfBirth: '',
    studies: '',
    campus: '',
    occupation: '',
    yearOfStudy: '',
    firstTime: false,
    ministry: '',
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Populate form when editing
  useEffect(() => {
    if (member) {
      setFormData({
        firstName: member.firstName,
        lastName: member.lastName,
        phoneNumber: member.phoneNumber,
        buildingAddress: member.buildingAddress,
        bornAgainStatus: member.bornAgainStatus,
        congregationGroup: member.congregationGroup,
        // New fields
        dateOfBirth: member.dateOfBirth || '',
        studies: member.studies || '',
        campus: member.campus || '',
        occupation: member.occupation || '',
        yearOfStudy: member.yearOfStudy || '',
        firstTime: member.firstTime || false,
        ministry: member.ministry || '',
      });
    } else {
      // Set default congregation to first available custom tab (not system tabs)
      const customTabs = congregations.filter(c =>
        !['overall', 'critical', 'new-believers'].includes(c.id)
      );
      if (customTabs.length > 0) {
        setFormData(prev => ({ ...prev, congregationGroup: customTabs[0].id }));
      } else {
        // If no custom tabs exist, default to new-believers
        setFormData(prev => ({ ...prev, congregationGroup: 'new-believers' }));
      }
    }
  }, [member, congregations]);

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    // Only validate that at least first name or last name is provided
    if (!formData.firstName.trim() && !formData.lastName.trim()) {
      newErrors.firstName = 'Please provide at least a first name or last name';
    }

    // Validate South African phone number format if provided
    if (formData.phoneNumber.trim()) {
      const cleaned = formData.phoneNumber.replace(/[\s\-\(\)]/g, '');
      if (!/^(\+27|0)[0-9]{9}$/.test(cleaned)) {
        newErrors.phoneNumber = 'Please enter a valid South African phone number (e.g., ************ or +27 82 123 4567)';
      }
    }

    // Congregation group is required
    if (!formData.congregationGroup) {
      newErrors.congregationGroup = 'Please select a group';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);
    
    try {
      await onSave(formData);
    } catch (error) {
      console.error('Error saving member:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleInputChange = (field: string, value: string | boolean) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const formatPhoneNumber = (value: string) => {
    const cleaned = value.replace(/\D/g, '');

    // Handle South African phone numbers
    if (cleaned.startsWith('27')) {
      // International format +27 XX XXX XXXX
      if (cleaned.length >= 11) {
        return `+27 ${cleaned.slice(2, 4)} ${cleaned.slice(4, 7)} ${cleaned.slice(7, 11)}`;
      } else if (cleaned.length >= 4) {
        return `+27 ${cleaned.slice(2, 4)} ${cleaned.slice(4)}`;
      } else {
        return `+27 ${cleaned.slice(2)}`;
      }
    } else if (cleaned.startsWith('0')) {
      // Local format 0XX XXX XXXX
      if (cleaned.length >= 10) {
        return `${cleaned.slice(0, 3)} ${cleaned.slice(3, 6)} ${cleaned.slice(6, 10)}`;
      } else if (cleaned.length >= 6) {
        return `${cleaned.slice(0, 3)} ${cleaned.slice(3, 6)} ${cleaned.slice(6)}`;
      } else if (cleaned.length >= 3) {
        return `${cleaned.slice(0, 3)} ${cleaned.slice(3)}`;
      } else {
        return cleaned;
      }
    } else {
      // If no prefix, assume local and add 0
      if (cleaned.length >= 9) {
        return `0${cleaned.slice(0, 2)} ${cleaned.slice(2, 5)} ${cleaned.slice(5, 9)}`;
      } else if (cleaned.length >= 5) {
        return `0${cleaned.slice(0, 2)} ${cleaned.slice(2, 5)} ${cleaned.slice(5)}`;
      } else if (cleaned.length >= 2) {
        return `0${cleaned.slice(0, 2)} ${cleaned.slice(2)}`;
      } else {
        return cleaned;
      }
    }
  };

  const handlePhoneChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const formatted = formatPhoneNumber(e.target.value);
    handleInputChange('phoneNumber', formatted);
  };

  // Include all congregations except Overall and Critical for selection
  const availableCongregations = congregations.filter(c =>
    !['overall', 'critical'].includes(c.id)
  );

  return (
    <div className="modern-modal-overlay" onClick={onCancel}>
      <div className="modern-modal-content" onClick={(e) => e.stopPropagation()}>
        <div className="modern-modal-header">
          <div className="modal-title-section">
            <div className="modal-icon">
              <User size={24} />
            </div>
            <h2>{member ? 'Edit Member' : 'Add New Member'}</h2>
          </div>
          <button
            type="button"
            className="modern-close-button"
            onClick={onCancel}
            aria-label="Close"
          >
            <X size={24} />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="modern-member-form">
          <div className="form-section">
            <h3 className="section-title">
              <User size={20} />
              Personal Information
            </h3>
            
            <div className="form-row">
              <div className="modern-form-group">
                <label htmlFor="firstName">
                  <User size={16} />
                  First Name
                </label>
                <input
                  type="text"
                  id="firstName"
                  value={formData.firstName}
                  onChange={(e) => handleInputChange('firstName', e.target.value)}
                  className={errors.firstName ? 'error' : ''}
                  placeholder="Enter first name"
                />
                {errors.firstName && <span className="modern-error-message">{errors.firstName}</span>}
              </div>

              <div className="modern-form-group">
                <label htmlFor="lastName">
                  <User size={16} />
                  Last Name
                </label>
                <input
                  type="text"
                  id="lastName"
                  value={formData.lastName}
                  onChange={(e) => handleInputChange('lastName', e.target.value)}
                  className={errors.lastName ? 'error' : ''}
                  placeholder="Enter last name"
                />
                {errors.lastName && <span className="modern-error-message">{errors.lastName}</span>}
              </div>
            </div>
          </div>

          <div className="form-section">
            <h3 className="section-title">
              <Phone size={20} />
              Contact Information
            </h3>

            <div className="modern-form-group">
              <label htmlFor="phoneNumber">
                <Phone size={16} />
                Phone Number
              </label>
              <input
                type="tel"
                id="phoneNumber"
                value={formData.phoneNumber}
                onChange={handlePhoneChange}
                className={errors.phoneNumber ? 'error' : ''}
                placeholder="+27 82 123 4567"
              />
              {errors.phoneNumber && <span className="modern-error-message">{errors.phoneNumber}</span>}
            </div>

            <div className="modern-form-group">
              <label htmlFor="buildingAddress">
                <MapPin size={16} />
                Residence
              </label>
              <textarea
                id="buildingAddress"
                value={formData.buildingAddress}
                onChange={(e) => handleInputChange('buildingAddress', e.target.value)}
                className={errors.buildingAddress ? 'error' : ''}
                placeholder="Enter residence address"
                rows={3}
              />
              {errors.buildingAddress && <span className="modern-error-message">{errors.buildingAddress}</span>}
            </div>
          </div>

          <div className="form-section">
            <h3 className="section-title">
              <Users size={20} />
              Church Information
            </h3>
            
            <div className="form-row">
              <div className="modern-form-group">
                <label htmlFor="congregationGroup">
                  <Users size={16} />
                  Group
                </label>
                <select
                  id="congregationGroup"
                  value={formData.congregationGroup}
                  onChange={(e) => handleInputChange('congregationGroup', e.target.value)}
                  className={errors.congregationGroup ? 'error' : ''}
                >
                  <option value="">Select a group</option>
                  {availableCongregations.map((congregation) => (
                    <option key={congregation.id} value={congregation.id}>
                      {congregation.name}
                    </option>
                  ))}
                </select>
                {errors.congregationGroup && <span className="modern-error-message">{errors.congregationGroup}</span>}
              </div>

              <div className="modern-form-group">
                <label className="modern-checkbox-label">
                  <input
                    type="checkbox"
                    checked={formData.bornAgainStatus}
                    onChange={(e) => handleInputChange('bornAgainStatus', e.target.checked)}
                    className="modern-checkbox"
                  />
                  <div className="checkbox-custom">
                    <Heart size={16} />
                  </div>
                  <span className="checkbox-text">Born Again</span>
                </label>
              </div>
            </div>
          </div>

          {/* New Believers Additional Information */}
          {formData.congregationGroup === 'new-believers' && (
            <div className="form-section">
              <h3 className="section-title">
                <Heart size={20} />
                New Believer Information
              </h3>

              <div className="form-row">
                <div className="modern-form-group">
                  <label htmlFor="dateOfBirth">
                    <Calendar size={16} />
                    Date of Birth
                  </label>
                  <input
                    type="date"
                    id="dateOfBirth"
                    value={formData.dateOfBirth}
                    onChange={(e) => handleInputChange('dateOfBirth', e.target.value)}
                    placeholder="Select date of birth"
                  />
                </div>

                <div className="modern-form-group">
                  <label htmlFor="studies">
                    <BookOpen size={16} />
                    Studies
                  </label>
                  <input
                    type="text"
                    id="studies"
                    value={formData.studies}
                    onChange={(e) => handleInputChange('studies', e.target.value)}
                    placeholder="Field of study"
                  />
                </div>
              </div>

              <div className="form-row">
                <div className="modern-form-group">
                  <label htmlFor="campus">
                    <GraduationCap size={16} />
                    Campus
                  </label>
                  <input
                    type="text"
                    id="campus"
                    value={formData.campus}
                    onChange={(e) => handleInputChange('campus', e.target.value)}
                    placeholder="Campus name"
                  />
                </div>

                <div className="modern-form-group">
                  <label htmlFor="occupation">
                    <Briefcase size={16} />
                    Occupation
                  </label>
                  <input
                    type="text"
                    id="occupation"
                    value={formData.occupation}
                    onChange={(e) => handleInputChange('occupation', e.target.value)}
                    placeholder="Current occupation"
                  />
                </div>
              </div>

              <div className="form-row">
                <div className="modern-form-group">
                  <label htmlFor="yearOfStudy">
                    <GraduationCap size={16} />
                    Year of Study
                  </label>
                  <input
                    type="text"
                    id="yearOfStudy"
                    value={formData.yearOfStudy}
                    onChange={(e) => handleInputChange('yearOfStudy', e.target.value)}
                    placeholder="e.g., 1st Year, 2nd Year"
                  />
                </div>

                <div className="modern-form-group">
                  <label htmlFor="ministry">
                    <Star size={16} />
                    Ministry
                  </label>
                  <select
                    id="ministry"
                    value={formData.ministry}
                    onChange={(e) => handleInputChange('ministry', e.target.value)}
                  >
                    <option value="">Select ministry</option>
                    <option value="Dancing Stars">Dancing Stars</option>
                    <option value="Choir">Choir</option>
                    <option value="Ushers">Ushers</option>
                    <option value="Media">Media</option>
                    <option value="Airport Stars">Airport Stars</option>
                    <option value="Arrival Stars">Arrival Stars</option>
                  </select>
                </div>
              </div>

              <div className="modern-form-group">
                <label className="modern-checkbox-label">
                  <input
                    type="checkbox"
                    checked={formData.firstTime}
                    onChange={(e) => handleInputChange('firstTime', e.target.checked)}
                    className="modern-checkbox"
                  />
                  <div className="checkbox-custom">
                    <Heart size={16} />
                  </div>
                  <span className="checkbox-text">First time giving life to Christ</span>
                </label>
              </div>
            </div>
          )}

          <div className="modern-form-actions">
            <button
              type="button"
              className="btn-secondary cancel-btn"
              onClick={onCancel}
              disabled={isSubmitting}
            >
              Cancel
            </button>
            <button
              type="submit"
              className="btn-primary submit-btn"
              disabled={isSubmitting}
            >
              {isSubmitting ? (
                <>
                  <div className="spinner" />
                  Saving...
                </>
              ) : (
                member ? 'Update Member' : 'Add Member'
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default ModernMemberForm;
