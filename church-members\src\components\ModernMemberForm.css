/* Modern Member Form Styles */

.modern-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(8px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: var(--spacing-lg);
  animation: fadeIn 0.3s ease-out;
}

.modern-modal-content {
  background: var(--surface-color);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-2xl);
  width: 100%;
  max-width: 700px;
  max-height: 90vh;
  overflow-y: auto;
  animation: slideInUp 0.3s ease-out;
}

.modern-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-xl);
  border-bottom: 1px solid var(--border-light);
  background: var(--gradient-primary);
  color: white;
  border-radius: var(--radius-xl) var(--radius-xl) 0 0;
  position: sticky;
  top: 0;
  z-index: 1;
}

.modal-title-section {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.modal-icon {
  width: 48px;
  height: 48px;
  border-radius: var(--radius-lg);
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10px);
}

.modern-modal-header h2 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 700;
}

.modern-close-button {
  width: 48px;
  height: 48px;
  border-radius: var(--radius-lg);
  background: rgba(255, 255, 255, 0.2);
  border: none;
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all var(--transition-fast);
  backdrop-filter: blur(10px);
}

.modern-close-button:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.05);
}

.modern-member-form {
  padding: var(--spacing-lg);
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

.form-section {
  background: var(--background-color);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  border: 1px solid var(--border-light);
}

.section-title {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  margin: 0 0 var(--spacing-lg) 0;
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-primary);
  padding-bottom: var(--spacing-sm);
  border-bottom: 2px solid var(--border-light);
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-md);
}

.modern-form-group {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-md);
}

.modern-form-group label {
  font-weight: 600;
  color: var(--text-primary);
  font-size: 0.875rem;
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.modern-form-group input,
.modern-form-group textarea,
.modern-form-group select {
  padding: var(--spacing-md) var(--spacing-lg);
  border: 2px solid var(--border-color);
  border-radius: var(--radius-lg);
  font-size: 1rem;
  font-family: inherit;
  background: var(--surface-color);
  transition: all var(--transition-fast);
  box-shadow: var(--shadow-sm);
}

.modern-form-group input:focus,
.modern-form-group textarea:focus,
.modern-form-group select:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 4px var(--primary-light), var(--shadow-md);
  background: white;
  transform: translateY(-1px);
}

.modern-form-group input.error,
.modern-form-group textarea.error,
.modern-form-group select.error {
  border-color: var(--danger-color);
  box-shadow: 0 0 0 4px var(--danger-light);
}

.modern-form-group textarea {
  resize: vertical;
  min-height: 100px;
  font-family: inherit;
}

.modern-checkbox-label {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  cursor: pointer;
  padding: var(--spacing-md);
  border-radius: var(--radius-lg);
  transition: all var(--transition-fast);
  background: var(--surface-color);
  border: 2px solid var(--border-color);
  margin-top: var(--spacing-sm);
}

.modern-checkbox-label:hover {
  background: var(--primary-light);
  border-color: var(--primary-color);
}

.modern-checkbox {
  display: none;
}

.checkbox-custom {
  width: 24px;
  height: 24px;
  border-radius: var(--radius-sm);
  border: 2px solid var(--border-color);
  background: var(--surface-color);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all var(--transition-fast);
  color: transparent;
}

.modern-checkbox:checked + .checkbox-custom {
  background: var(--gradient-success);
  border-color: var(--success-color);
  color: white;
  transform: scale(1.1);
}

.checkbox-text {
  font-weight: 600;
  color: var(--text-primary);
  user-select: none;
}

.modern-error-message {
  color: var(--danger-color);
  font-size: 0.75rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  background: var(--danger-light);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-sm);
  border: 1px solid rgba(239, 68, 68, 0.2);
}

.modern-form-actions {
  display: flex;
  gap: var(--spacing-md);
  justify-content: flex-end;
  padding-top: var(--spacing-lg);
  border-top: 1px solid var(--border-light);
  margin-top: var(--spacing-lg);
}

.cancel-btn,
.submit-btn {
  min-width: 140px;
  padding: var(--spacing-md) var(--spacing-xl);
  font-weight: 600;
  border-radius: var(--radius-lg);
  transition: all var(--transition-fast);
}

.submit-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
}

.spinner {
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* Mobile responsive */
@media (max-width: 768px) {
  .modern-modal-overlay {
    padding: var(--spacing-md);
  }
  
  .modern-modal-content {
    max-height: 95vh;
  }
  
  .modern-modal-header {
    padding: var(--spacing-lg);
  }
  
  .modal-title-section {
    gap: var(--spacing-sm);
  }
  
  .modal-icon {
    width: 40px;
    height: 40px;
  }
  
  .modern-modal-header h2 {
    font-size: 1.25rem;
  }
  
  .modern-member-form {
    padding: var(--spacing-lg);
  }
  
  .form-section {
    padding: var(--spacing-lg);
  }
  
  .form-row {
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
  }
  
  .modern-form-actions {
    flex-direction: column-reverse;
  }
  
  .cancel-btn,
  .submit-btn {
    width: 100%;
  }
}

@media (max-width: 480px) {
  .modern-modal-overlay {
    padding: var(--spacing-sm);
  }
  
  .modern-modal-header {
    padding: var(--spacing-md);
  }
  
  .modern-member-form {
    padding: var(--spacing-md);
  }
  
  .form-section {
    padding: var(--spacing-md);
  }
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
