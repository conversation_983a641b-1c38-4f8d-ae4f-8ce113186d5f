import { useState, useEffect } from 'react';
import { db } from './services/database';
import type { Member, AttendanceRecord, Congregation, MemberWithAttendance } from './types';
import { enhanceMembersWithAttendance, getCriticalMembers } from './utils/attendanceUtils';
import { getCurrentMonthSundays } from './utils/dateUtils';
import ModernTabNavigation from './components/ModernTabNavigation';
import ExcelTableView from './components/ExcelTableView';
import ModernMemberTable from './components/ModernMemberTable';
import ListView from './components/ListView';
import StatsView from './components/StatsView';
import ModernMemberForm from './components/ModernMemberForm';
import TabManagement from './components/TabManagement';
import ViewSelector, { ViewType } from './components/ViewSelector';
import './App.css';

function App() {
  const [members, setMembers] = useState<Member[]>([]);
  const [attendanceRecords, setAttendanceRecords] = useState<AttendanceRecord[]>([]);
  const [congregations, setCongregations] = useState<Congregation[]>([]);
  const [activeTab, setActiveTab] = useState<string>('');
  const [currentView, setCurrentView] = useState<ViewType>('table');
  const [showMemberForm, setShowMemberForm] = useState(false);
  const [editingMember, setEditingMember] = useState<Member | null>(null);
  const [showTabManagement, setShowTabManagement] = useState(false);
  const [loading, setLoading] = useState(true);

  // Load data on component mount
  useEffect(() => {
    loadData();

    // Add debug methods to window for manual reset
    (window as any).resetChurchTabs = () => {
      db.forceResetToDefaultTabs();
      loadData();
      console.log('Church tabs reset to default: Overall, New Believers, Critical');
    };

    (window as any).clearAllData = () => {
      localStorage.clear();
      window.location.reload();
      console.log('All data cleared and page reloaded');
    };
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);
      const [membersData, attendanceData, congregationsData] = await Promise.all([
        Promise.resolve(db.getMembers()),
        Promise.resolve(db.getAttendanceRecords()),
        Promise.resolve(db.getCongregations()),
      ]);

      setMembers(membersData);
      setAttendanceRecords(attendanceData);
      setCongregations(congregationsData);

      // Set Overall tab as active by default
      if (!activeTab && congregationsData.length > 0) {
        setActiveTab('overall');
      }
    } catch (error) {
      console.error('Error loading data:', error);
    } finally {
      setLoading(false);
    }
  };

  // Get enhanced members with attendance data
  const membersWithAttendance: MemberWithAttendance[] = enhanceMembersWithAttendance(
    members,
    attendanceRecords
  );

  // Filter members based on active tab
  const getFilteredMembers = (): MemberWithAttendance[] => {
    if (activeTab === 'overall') {
      // Overall tab shows all members across all tabs
      return membersWithAttendance;
    } else if (activeTab === 'critical') {
      // Critical tab shows only members with 2+ consecutive absences
      return getCriticalMembers(membersWithAttendance);
    } else if (activeTab === 'new-believers') {
      // New Believers tab shows members assigned to this tab
      return membersWithAttendance.filter(member => member.congregationGroup === 'new-believers');
    } else {
      // Filter by congregation for custom tabs
      return membersWithAttendance.filter(member => member.congregationGroup === activeTab);
    }
  };

  const filteredMembers = getFilteredMembers();
  const criticalCount = getCriticalMembers(membersWithAttendance).length;
  const newBelieversCount = membersWithAttendance.filter(member => member.congregationGroup === 'new-believers').length;

  // Get current month Sundays for attendance columns
  const currentMonthSundays = getCurrentMonthSundays();

  // Handle member save
  const handleSaveMember = async (memberData: Omit<Member, 'id' | 'createdDate' | 'lastUpdated'>) => {
    try {
      if (editingMember) {
        // Update existing member
        const updatedMember = db.updateMember(editingMember.id, memberData);
        if (updatedMember) {
          setMembers(prev => prev.map(m => m.id === editingMember.id ? updatedMember : m));
        }
      } else {
        // Create new member
        const newMember = db.saveMember(memberData);
        setMembers(prev => [...prev, newMember]);
      }

      setShowMemberForm(false);
      setEditingMember(null);
    } catch (error) {
      console.error('Error saving member:', error);
    }
  };

  // Handle member delete
  const handleDeleteMember = async (memberId: string) => {
    if (window.confirm('Are you sure you want to delete this member?')) {
      try {
        const success = db.deleteMember(memberId);
        if (success) {
          setMembers(prev => prev.filter(m => m.id !== memberId));
          setAttendanceRecords(prev => prev.filter(r => r.memberId !== memberId));
        }
      } catch (error) {
        console.error('Error deleting member:', error);
      }
    }
  };

  // Handle attendance toggle
  const handleAttendanceToggle = async (memberId: string, date: string, currentStatus: 'present' | 'absent' | null) => {
    try {
      const newStatus: 'present' | 'absent' = currentStatus === 'present' ? 'absent' : 'present';

      const attendanceRecord = db.saveAttendanceRecord({
        memberId,
        date,
        status: newStatus,
      });

      setAttendanceRecords(prev => {
        const filtered = prev.filter(r => !(r.memberId === memberId && r.date === date));
        return [...filtered, attendanceRecord];
      });
    } catch (error) {
      console.error('Error updating attendance:', error);
    }
  };

  if (loading) {
    return (
      <div className="loading-screen">
        <div className="loading-spinner"></div>
        <p>Loading your church data...</p>
      </div>
    );
  }

  return (
    <div className="modern-app">
      <header className="modern-header">
        <div className="header-content">
          <h1 className="app-title">Church Membership Management</h1>
          <button
            className="btn-secondary manage-tabs-btn"
            onClick={() => setShowTabManagement(true)}
          >
            Manage Tabs
          </button>
        </div>
      </header>

      <main className="modern-main">
        <ModernTabNavigation
          congregations={congregations}
          activeTab={activeTab}
          onTabChange={setActiveTab}
          criticalCount={criticalCount}
          membersCount={filteredMembers.length}
          newBelieversCount={newBelieversCount}
        />

        <div className="content-area">
          {congregations.length > 0 ? (
            <>
              <div className="action-bar">
                <button
                  className="btn-primary add-member-btn"
                  onClick={() => setShowMemberForm(true)}
                >
                  <span className="btn-icon">+</span>
                  Add New Member
                </button>

                <ViewSelector
                  currentView={currentView}
                  onViewChange={setCurrentView}
                />

                <div className="member-stats">
                  <span className="member-count">{filteredMembers.length} member{filteredMembers.length !== 1 ? 's' : ''}</span>
                  {criticalCount > 0 && (
                    <span className="critical-alert">
                      {criticalCount} need attention
                    </span>
                  )}
                </div>
              </div>
            </>
          ) : (
            <div className="welcome-screen">
              <div className="welcome-content">
                <h2>Welcome to Church Membership Management</h2>
                <p>Get started by creating your first tab to organize your members.</p>
                <button
                  className="btn-primary welcome-btn"
                  onClick={() => setShowTabManagement(true)}
                >
                  Create Your First Tab
                </button>
              </div>
            </div>
          )}

              {currentView === 'table' && (
                <ExcelTableView
                  members={filteredMembers}
                  attendanceRecords={attendanceRecords}
                  sundays={currentMonthSundays}
                  onEditMember={(member) => {
                    setEditingMember(member);
                    setShowMemberForm(true);
                  }}
                  onDeleteMember={handleDeleteMember}
                  onAttendanceToggle={handleAttendanceToggle}
                />
              )}

              {currentView === 'cards' && (
                <ModernMemberTable
                  members={filteredMembers}
                  attendanceRecords={attendanceRecords}
                  sundays={currentMonthSundays}
                  activeTab={activeTab}
                  onEditMember={(member) => {
                    setEditingMember(member);
                    setShowMemberForm(true);
                  }}
                  onDeleteMember={handleDeleteMember}
                  onAttendanceToggle={handleAttendanceToggle}
                />
              )}

              {currentView === 'list' && (
                <ListView
                  members={filteredMembers}
                  onEditMember={(member) => {
                    setEditingMember(member);
                    setShowMemberForm(true);
                  }}
                  onDeleteMember={handleDeleteMember}
                />
              )}

              {currentView === 'stats' && (
                <StatsView
                  members={membersWithAttendance}
                  attendanceRecords={attendanceRecords}
                  congregations={congregations}
                />
              )}
        </div>
      </main>

      {showMemberForm && (
        <ModernMemberForm
          member={editingMember}
          congregations={congregations}
          onSave={handleSaveMember}
          onCancel={() => {
            setShowMemberForm(false);
            setEditingMember(null);
          }}
        />
      )}

      {showTabManagement && (
        <TabManagement
          congregations={congregations}
          onSave={(newCongregations) => {
            setCongregations(newCongregations);
            setShowTabManagement(false);
          }}
          onCancel={() => setShowTabManagement(false)}
        />
      )}
    </div>
  );
}

export default App;
