import React, { useState } from 'react';
import type { Congregation } from '../types';
import { X, Plus, Edit, Trash2, Settings } from 'lucide-react';
import { db } from '../services/database';
import './TabManagement.css';

interface TabManagementProps {
  congregations: Congregation[];
  onSave: (congregations: Congregation[]) => void;
  onCancel: () => void;
}

const TabManagement: React.FC<TabManagementProps> = ({
  congregations,
  onSave,
  onCancel,
}) => {
  const [localCongregations, setLocalCongregations] = useState<Congregation[]>(congregations);
  const [showAddForm, setShowAddForm] = useState(false);
  const [editingTab, setEditingTab] = useState<Congregation | null>(null);
  const [newTabData, setNewTabData] = useState({ name: '', description: '' });

  const systemTabs = ['overall', 'critical', 'new-believers'];
  const userTabs = localCongregations.filter(c => !systemTabs.includes(c.id));

  const handleAddTab = () => {
    if (!newTabData.name.trim()) return;

    // Create the user's custom tab
    const newTab = db.saveCongregation({
      name: newTabData.name,
      description: newTabData.description,
    });

    setLocalCongregations(prev => [...prev, newTab]);
    setNewTabData({ name: '', description: '' });
    setShowAddForm(false);
  };

  const handleEditTab = (tab: Congregation) => {
    if (!newTabData.name.trim()) return;

    const updatedTab = db.updateCongregation(tab.id, {
      name: newTabData.name,
      description: newTabData.description,
    });

    if (updatedTab) {
      setLocalCongregations(prev => 
        prev.map(c => c.id === tab.id ? updatedTab : c)
      );
    }

    setEditingTab(null);
    setNewTabData({ name: '', description: '' });
  };

  const handleDeleteTab = (tabId: string) => {
    if (window.confirm('Are you sure you want to delete this tab? All members in this tab will need to be reassigned.')) {
      db.deleteCongregation(tabId);
      setLocalCongregations(prev => prev.filter(c => c.id !== tabId));
    }
  };

  const startEdit = (tab: Congregation) => {
    setEditingTab(tab);
    setNewTabData({ name: tab.name, description: tab.description || '' });
    setShowAddForm(false);
  };

  const cancelEdit = () => {
    setEditingTab(null);
    setShowAddForm(false);
    setNewTabData({ name: '', description: '' });
  };

  const handleSave = () => {
    onSave(localCongregations);
  };

  return (
    <div className="tab-management-overlay" onClick={onCancel}>
      <div className="tab-management-content" onClick={(e) => e.stopPropagation()}>
        <div className="tab-management-header">
          <div className="header-title-section">
            <div className="header-icon">
              <Settings size={24} />
            </div>
            <h2>Manage Tabs</h2>
          </div>
          <button
            type="button"
            className="close-button"
            onClick={onCancel}
            aria-label="Close"
          >
            <X size={24} />
          </button>
        </div>

        <div className="tab-management-body">
          <div className="system-tabs-section">
            <h3>System Tabs</h3>
            <p className="section-description">These tabs are built-in and cannot be modified</p>
            <div className="tabs-list">
              {localCongregations.filter(c => systemTabs.includes(c.id)).map(tab => (
                <div key={tab.id} className="tab-item system">
                  <div className="tab-info">
                    <span className="tab-name">{tab.name}</span>
                    <span className="tab-description">{tab.description}</span>
                  </div>
                  <div className="tab-badge system">System</div>
                </div>
              ))}
            </div>
          </div>

          <div className="user-tabs-section">
            <div className="section-header">
              <h3>Custom Tabs</h3>
              <button
                className="btn-primary add-tab-btn"
                onClick={() => {
                  setShowAddForm(true);
                  setEditingTab(null);
                  setNewTabData({ name: '', description: '' });
                }}
              >
                <Plus size={16} />
                Add Tab
              </button>
            </div>
            <p className="section-description">Create custom tabs to organize your members</p>

            {(showAddForm || editingTab) && (
              <div className="tab-form">
                <div className="form-group">
                  <label>Tab Name *</label>
                  <input
                    type="text"
                    value={newTabData.name}
                    onChange={(e) => setNewTabData(prev => ({ ...prev, name: e.target.value }))}
                    placeholder="Enter tab name"
                    autoFocus
                  />
                </div>
                <div className="form-group">
                  <label>Description</label>
                  <input
                    type="text"
                    value={newTabData.description}
                    onChange={(e) => setNewTabData(prev => ({ ...prev, description: e.target.value }))}
                    placeholder="Enter description (optional)"
                  />
                </div>
                <div className="form-actions">
                  <button
                    type="button"
                    className="btn-secondary"
                    onClick={cancelEdit}
                  >
                    Cancel
                  </button>
                  <button
                    type="button"
                    className="btn-primary"
                    onClick={editingTab ? () => handleEditTab(editingTab) : handleAddTab}
                    disabled={!newTabData.name.trim()}
                  >
                    {editingTab ? 'Update' : 'Add'} Tab
                  </button>
                </div>
              </div>
            )}

            <div className="tabs-list">
              {userTabs.map(tab => (
                <div key={tab.id} className="tab-item user">
                  <div className="tab-info">
                    <span className="tab-name">{tab.name}</span>
                    <span className="tab-description">{tab.description || 'No description'}</span>
                  </div>
                  <div className="tab-actions">
                    <button
                      className="action-btn edit"
                      onClick={() => startEdit(tab)}
                      title="Edit tab"
                    >
                      <Edit size={16} />
                    </button>
                    <button
                      className="action-btn delete"
                      onClick={() => handleDeleteTab(tab.id)}
                      title="Delete tab"
                    >
                      <Trash2 size={16} />
                    </button>
                  </div>
                </div>
              ))}
              
              {userTabs.length === 0 && !showAddForm && (
                <div className="empty-state">
                  <p>No custom tabs yet. Create your first tab to organize members!</p>
                </div>
              )}
            </div>
          </div>
        </div>

        <div className="tab-management-footer">
          <button
            type="button"
            className="btn-secondary"
            onClick={onCancel}
          >
            Cancel
          </button>
          <button
            type="button"
            className="btn-primary"
            onClick={handleSave}
          >
            Save Changes
          </button>
        </div>
      </div>
    </div>
  );
};

export default TabManagement;
