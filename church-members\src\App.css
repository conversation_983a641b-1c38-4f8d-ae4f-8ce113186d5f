/* Modern Church Membership App Styles */

.modern-app {
  min-height: 100vh;
  background: var(--background-color);
  display: flex;
  flex-direction: column;
}

.modern-header {
  background: var(--gradient-primary);
  color: white;
  padding: var(--spacing-lg) 0;
  box-shadow: var(--shadow-xl);
  position: relative;
  overflow: hidden;
}

.modern-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  pointer-events: none;
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-lg);
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  z-index: 1;
}

.app-title-section {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.app-title {
  margin: 0;
  font-size: 1.75rem;
  font-weight: 700;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.manage-tabs-btn {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(10px);
}

.manage-tabs-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
}

.modern-main {
  flex: 1;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
  padding: var(--spacing-xl) var(--spacing-lg);
}

.content-area {
  background: var(--surface-color);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-lg);
  overflow: hidden;
  margin-top: var(--spacing-lg);
}

.action-bar {
  padding: var(--spacing-xl);
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border-bottom: 1px solid var(--border-light);
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: var(--spacing-lg);
  flex-wrap: wrap;
}

.add-member-btn {
  font-size: 1rem;
  padding: var(--spacing-md) var(--spacing-xl);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
}

.btn-icon {
  font-size: 1.25rem;
  font-weight: 700;
}

.member-stats {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.member-count {
  color: var(--text-secondary);
  font-size: 0.875rem;
  font-weight: 600;
  background: var(--surface-color);
  padding: var(--spacing-xs) var(--spacing-md);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-sm);
}

.critical-alert {
  background: var(--gradient-danger);
  color: white;
  padding: var(--spacing-xs) var(--spacing-md);
  border-radius: var(--radius-md);
  font-size: 0.75rem;
  font-weight: 600;
  animation: pulse 2s infinite;
  box-shadow: var(--shadow-md);
}

.loading-screen {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background: var(--gradient-primary);
  color: white;
  gap: var(--spacing-lg);
}

.loading-spinner {
  width: 60px;
  height: 60px;
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-top: 4px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes pulse {
  0%, 100% { opacity: 1; transform: scale(1); }
  50% { opacity: 0.8; transform: scale(1.05); }
}

/* Welcome Screen */
.welcome-screen {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
  padding: var(--spacing-3xl);
}

.welcome-content {
  text-align: center;
  max-width: 500px;
}

.welcome-content h2 {
  margin: 0 0 var(--spacing-lg) 0;
  font-size: 2rem;
  font-weight: 700;
  color: var(--text-primary);
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.welcome-content p {
  margin: 0 0 var(--spacing-xl) 0;
  font-size: 1.125rem;
  color: var(--text-secondary);
  line-height: 1.6;
}

.welcome-btn {
  font-size: 1.125rem;
  padding: var(--spacing-lg) var(--spacing-2xl);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-xl);
}

/* Mobile responsive adjustments */
@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    gap: var(--spacing-md);
    text-align: center;
  }

  .app-title {
    font-size: 1.5rem;
  }

  .modern-main {
    padding: var(--spacing-lg) var(--spacing-md);
  }

  .action-bar {
    flex-direction: column;
    gap: var(--spacing-md);
    padding: var(--spacing-lg);
    align-items: stretch;
  }

  .member-stats {
    justify-content: center;
    width: 100%;
    order: 3;
  }

  .add-member-btn {
    width: 100%;
    justify-content: center;
    order: 1;
  }

  .view-selector {
    order: 2;
  }
}

@media (max-width: 480px) {
  .app-title {
    font-size: 1.25rem;
  }

  .modern-main {
    padding: var(--spacing-md);
  }

  .action-bar {
    padding: var(--spacing-md);
  }
}

/* Utility classes */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: var(--spacing-xs); }
.mb-2 { margin-bottom: var(--spacing-sm); }
.mb-3 { margin-bottom: var(--spacing-md); }
.mb-4 { margin-bottom: var(--spacing-lg); }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: var(--spacing-xs); }
.mt-2 { margin-top: var(--spacing-sm); }
.mt-3 { margin-top: var(--spacing-md); }
.mt-4 { margin-top: var(--spacing-lg); }

.flex {
  display: flex;
}

.flex-col {
  flex-direction: column;
}

.items-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.gap-1 { gap: var(--spacing-xs); }
.gap-2 { gap: var(--spacing-sm); }
.gap-3 { gap: var(--spacing-md); }
.gap-4 { gap: var(--spacing-lg); }
